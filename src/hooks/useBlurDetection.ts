/**
 * Hook for using blur detection with react-native-vision-camera
 */

import { useCallback, useRef, useState } from 'react';
import {
  BlurDetectionService,
  BlurDetectionResult,
  BlurDetectionConfig,
} from '~/services/camera/BlurDetectionService';

export interface UseBlurDetectionOptions extends BlurDetectionConfig {
  onBlurDetected?: (result: BlurDetectionResult) => void;
  onSharpDetected?: (result: BlurDetectionResult) => void;
}

export interface UseBlurDetectionReturn {
  frameProcessor: any;
  lastResult: BlurDetectionResult | null;
  isBlurry: boolean;
  blurScore: number;
  updateConfig: (config: Partial<BlurDetectionConfig>) => void;
}

/**
 * Custom hook for blur detection in camera frames
 */
export function useBlurDetection(
  options: UseBlurDetectionOptions = {},
): UseBlurDetectionReturn {
  const { onBlurDetected, onSharpDetected, ...config } = options;

  const serviceRef = useRef(new BlurDetectionService(config));
  const [lastResult, setLastResult] = useState<BlurDetectionResult | null>(
    null,
  );

  const handleBlurResult = useCallback(
    (result: BlurDetectionResult) => {
      setLastResult(result);

      if (result.isBlurry && onBlurDetected) {
        onBlurDetected(result);
      } else if (!result.isBlurry && onSharpDetected) {
        onSharpDetected(result);
      }
    },
    [onBlurDetected, onSharpDetected],
  );

  const frameProcessor =
    serviceRef.current.createFrameProcessor(handleBlurResult);

  const updateConfig = useCallback(
    (newConfig: Partial<BlurDetectionConfig>) => {
      serviceRef.current.updateConfig(newConfig);
    },
    [],
  );

  return {
    frameProcessor,
    lastResult,
    isBlurry: lastResult?.isBlurry ?? false,
    blurScore: lastResult?.blurScore ?? 0,
    updateConfig,
  };
}

/**
 * Hook for simple blur detection with boolean result
 */
export function useSimpleBlurDetection(
  threshold: number = 100,
  onBlurChange?: (isBlurry: boolean) => void,
): {
  frameProcessor: any;
  isBlurry: boolean;
  blurScore: number;
} {
  const { frameProcessor, isBlurry, blurScore } = useBlurDetection({
    threshold,
    enableLogging: false,
    onBlurDetected: () => {
      if (onBlurChange && !isBlurry) {
        onBlurChange(true);
      }
    },
    onSharpDetected: () => {
      if (onBlurChange && isBlurry) {
        onBlurChange(false);
      }
    },
  });

  return {
    frameProcessor,
    isBlurry,
    blurScore,
  };
}
