/**
 * Optimized hook for blur detection with OpenCV support
 * Falls back to JavaScript implementation if OpenCV is not available
 */

import { useCallback, useRef, useState } from 'react';
import { 
  BlurDetectionResult, 
  BlurDetectionConfig 
} from '~/services/camera/BlurDetectionService';
import { OpenCVBlurDetectionService } from '~/services/camera/OpenCVBlurDetectionService';
import { BlurDetectionService } from '~/services/camera/BlurDetectionService';

export interface UseOptimizedBlurDetectionOptions extends BlurDetectionConfig {
  onBlurDetected?: (result: BlurDetectionResult) => void;
  onSharpDetected?: (result: BlurDetectionResult) => void;
  useOpenCV?: boolean; // Whether to use OpenCV (if available) or JavaScript implementation
}

export interface UseOptimizedBlurDetectionReturn {
  frameProcessor: any;
  lastResult: BlurDetectionResult | null;
  isBlurry: boolean;
  blurScore: number;
  updateConfig: (config: Partial<BlurDetectionConfig>) => void;
  isUsingOpenCV: boolean;
}

/**
 * Custom hook for optimized blur detection
 * Automatically uses OpenCV if available, falls back to JavaScript implementation
 */
export function useOptimizedBlurDetection(
  options: UseOptimizedBlurDetectionOptions = {}
): UseOptimizedBlurDetectionReturn {
  const {
    onBlurDetected,
    onSharpDetected,
    useOpenCV = true,
    ...config
  } = options;

  // Try to use OpenCV first, fall back to JavaScript implementation
  const [isUsingOpenCV, setIsUsingOpenCV] = useState(false);
  const serviceRef = useRef<OpenCVBlurDetectionService | BlurDetectionService>();
  
  // Initialize service
  if (!serviceRef.current) {
    if (useOpenCV) {
      try {
        serviceRef.current = new OpenCVBlurDetectionService(config);
        setIsUsingOpenCV(true);
      } catch (error) {
        console.warn('[useOptimizedBlurDetection] OpenCV not available, using JavaScript implementation');
        serviceRef.current = new BlurDetectionService(config);
        setIsUsingOpenCV(false);
      }
    } else {
      serviceRef.current = new BlurDetectionService(config);
      setIsUsingOpenCV(false);
    }
  }

  const [lastResult, setLastResult] = useState<BlurDetectionResult | null>(null);

  const handleBlurResult = useCallback((result: BlurDetectionResult) => {
    setLastResult(result);

    if (result.isBlurry && onBlurDetected) {
      onBlurDetected(result);
    } else if (!result.isBlurry && onSharpDetected) {
      onSharpDetected(result);
    }
  }, [onBlurDetected, onSharpDetected]);

  const frameProcessor = serviceRef.current.createFrameProcessor(handleBlurResult);

  const updateConfig = useCallback((newConfig: Partial<BlurDetectionConfig>) => {
    serviceRef.current?.updateConfig(newConfig);
  }, []);

  return {
    frameProcessor,
    lastResult,
    isBlurry: lastResult?.isBlurry ?? false,
    blurScore: lastResult?.blurScore ?? 0,
    updateConfig,
    isUsingOpenCV,
  };
}

/**
 * Hook specifically for OpenCV blur detection
 * Throws error if OpenCV is not available
 */
export function useOpenCVBlurDetection(
  options: UseOptimizedBlurDetectionOptions = {}
): UseOptimizedBlurDetectionReturn {
  const result = useOptimizedBlurDetection({ ...options, useOpenCV: true });
  
  if (!result.isUsingOpenCV) {
    throw new Error('OpenCV is not available. Please install react-native-fast-opencv');
  }
  
  return result;
}

/**
 * Hook for performance comparison between implementations
 */
export function useBlurDetectionComparison() {
  const jsService = useRef(new BlurDetectionService());
  const openCVService = useRef(new OpenCVBlurDetectionService());
  
  const [performanceResults, setPerformanceResults] = useState<{
    javascript: number;
    opencv: number;
    improvement: number;
  } | null>(null);

  const runPerformanceTest = useCallback((frame: any, iterations: number = 50) => {
    'worklet';
    
    // Test JavaScript implementation
    const jsStart = Date.now();
    for (let i = 0; i < iterations; i++) {
      // Note: This would need actual frame data for real testing
      // jsService.current.processFrame(mockImageData, frame.width, frame.height);
    }
    const jsTime = (Date.now() - jsStart) / iterations;

    // Test OpenCV implementation
    const openCVStart = Date.now();
    for (let i = 0; i < iterations; i++) {
      openCVService.current.processFrame(frame);
    }
    const openCVTime = (Date.now() - openCVStart) / iterations;

    const improvement = ((jsTime - openCVTime) / jsTime) * 100;

    const results = {
      javascript: jsTime,
      opencv: openCVTime,
      improvement: improvement,
    };

    setPerformanceResults(results);
    
    console.log(`[Performance Comparison]
      JavaScript: ${jsTime.toFixed(2)}ms per frame
      OpenCV: ${openCVTime.toFixed(2)}ms per frame
      Improvement: ${improvement.toFixed(1)}% faster with OpenCV
    `);

    return results;
  }, []);

  return {
    performanceResults,
    runPerformanceTest,
  };
}
