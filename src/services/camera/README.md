# Blur Detection Service

This service provides real-time blur detection for camera frames using the Laplacian variation algorithm with react-native-vision-camera and react-native-worklets-core.

## Features

- **Real-time blur detection** using Laplacian variance algorithm
- **Worklet-based processing** for optimal performance on the camera thread
- **Configurable sensitivity** and performance settings
- **Frame skipping** for performance optimization
- **TypeScript support** with full type definitions
- **Comprehensive testing** with Jest

## Installation

The required dependencies are already installed in this project:
- `react-native-vision-camera` (v4.6.3)
- `react-native-worklets-core` (v1.6.0)

## Usage

### Basic Usage with Hook

```typescript
import { useBlurDetection } from '~/hooks/useBlurDetection';

const MyCameraComponent = () => {
  const { frameProcessor, isBlurry, blurScore } = useBlurDetection({
    threshold: 100,
    enableLogging: __DEV__,
  });

  return (
    <Camera
      device={device}
      isActive={true}
      frameProcessor={frameProcessor}
    />
  );
};
```

### Advanced Usage with Callbacks

```typescript
const { frameProcessor, isBlurry, blurScore, updateConfig } = useBlurDetection({
  threshold: 100,
  skipFrames: 2,
  enableLogging: __DEV__,
  onBlurDetected: (result) => {
    console.log('Blur detected!', result);
    // Handle blur detection
  },
  onSharpDetected: (result) => {
    console.log('Sharp image detected!', result);
    // Handle sharp image detection
  },
});

// Update configuration dynamically
updateConfig({ threshold: 150 });
```

### Direct Service Usage

```typescript
import { BlurDetectionService } from '~/services/camera/BlurDetectionService';

const service = new BlurDetectionService({
  threshold: 100,
  sampleSize: 100,
  enableLogging: false,
  skipFrames: 2,
});

// Process a frame manually
const result = service.processFrame(imageData, width, height);
if (result) {
  console.log('Blur result:', result);
}

// Create frame processor for vision-camera
const frameProcessor = service.createFrameProcessor((result) => {
  console.log('Frame processed:', result);
});
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `threshold` | number | 100 | Blur detection threshold (lower = more sensitive) |
| `sampleSize` | number | 100 | Size to resize frames for processing (performance) |
| `enableLogging` | boolean | false | Enable console logging for debugging |
| `skipFrames` | number | 2 | Skip N frames between processing (performance) |

## Algorithm Details

The blur detection uses the **Laplacian variance method**:

1. **Convert to grayscale** using luminance formula: `0.299*R + 0.587*G + 0.114*B`
2. **Apply Laplacian kernel** to detect edges:
   ```
   [ 0, -1,  0]
   [-1,  4, -1]
   [ 0, -1,  0]
   ```
3. **Calculate variance** of the Laplacian result
4. **Compare to threshold** - lower variance indicates blur

### Why Laplacian Variance?

- **Fast computation** suitable for real-time processing
- **Reliable detection** of blur vs sharp images
- **Well-established** computer vision technique
- **Configurable sensitivity** via threshold adjustment

## Performance Considerations

- **Frame skipping**: Process every Nth frame to reduce CPU usage
- **Image resizing**: Downscale frames before processing
- **Worklet execution**: Runs on camera thread, not blocking UI
- **Memory efficient**: Minimal allocations during processing

### Typical Performance

- **Processing time**: ~1-5ms per frame (depending on resolution)
- **Memory usage**: Minimal additional overhead
- **CPU impact**: Low when using frame skipping

## Integration with Existing Camera

The blur detection is already integrated into `CameraViewScreen.tsx`:

```typescript
// In CameraViewScreen.tsx
const { frameProcessor, isBlurry, blurScore } = useBlurDetection({
  threshold: 100,
  enableLogging: __DEV__,
  skipFrames: 2,
});

// Added to Camera component
<Camera
  // ... other props
  frameProcessor={!isBarCodeScanner ? frameProcessor : undefined}
/>
```

## Testing

Run the test suite:

```bash
npm test -- src/services/camera/__tests__/BlurDetectionService.test.ts
```

Tests cover:
- Configuration management
- Frame processing accuracy
- Error handling
- Performance features (frame skipping)

## Troubleshooting

### Common Issues

1. **"Worklets module not found"**
   - Ensure `react-native-worklets-core` is properly installed
   - Check that the Babel plugin is configured in `babel.config.js`

2. **Poor blur detection accuracy**
   - Adjust the `threshold` value (lower = more sensitive)
   - Ensure adequate lighting conditions
   - Check that frames are in RGB format

3. **Performance issues**
   - Increase `skipFrames` value
   - Reduce `sampleSize` for faster processing
   - Ensure device has sufficient processing power

### Debugging

Enable logging to see detailed information:

```typescript
const { frameProcessor } = useBlurDetection({
  enableLogging: true, // Enable detailed logging
  threshold: 100,
});
```

## Future Enhancements

If JavaScript worklets aren't performant enough, consider:

1. **Native frame processor plugins** for iOS/Android
2. **GPU-accelerated processing** using Metal/OpenGL
3. **Advanced algorithms** like FFT-based blur detection
4. **Machine learning models** for more sophisticated detection

## Example Component

See `src/components/camera/BlurDetectionExample.tsx` for a complete example with:
- Real-time blur status display
- Sensitivity controls
- Blur history tracking
- Performance monitoring
