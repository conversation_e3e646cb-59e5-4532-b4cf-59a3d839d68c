/**
 * Example component demonstrating blur detection usage
 * This can be used as a reference for implementing blur detection in other components
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import {
  Camera,
  useCameraDevices,
  getCameraDevice,
} from 'react-native-vision-camera';
import { useBlurDetection } from '~/hooks/useBlurDetection';
import colors from '~/styles/colors';

interface BlurDetectionExampleProps {
  onBlurStatusChange?: (isBlurry: boolean) => void;
}

const BlurDetectionExample: React.FC<BlurDetectionExampleProps> = ({
  onBlurStatusChange,
}) => {
  const [blurHistory, setBlurHistory] = useState<boolean[]>([]);
  const devices = useCameraDevices();
  const device = getCameraDevice(devices, 'back');

  const { frameProcessor, isBlurry, blurScore, updateConfig } =
    useBlurDetection({
      threshold: 100,
      enableLogging: __DEV__,
      skipFrames: 2,
      onBlurDetected: result => {
        console.log('Blur detected:', result);
        setBlurHistory(prev => [...prev.slice(-9), true]);
        onBlurStatusChange?.(true);
      },
      onSharpDetected: result => {
        console.log('Sharp image detected:', result);
        setBlurHistory(prev => [...prev.slice(-9), false]);
        onBlurStatusChange?.(false);
      },
    });

  const handleSensitivityChange = (increase: boolean) => {
    const currentConfig = { threshold: 100 }; // You'd get this from the service
    const newThreshold = increase
      ? currentConfig.threshold + 20
      : Math.max(20, currentConfig.threshold - 20);

    updateConfig({ threshold: newThreshold });

    Alert.alert(
      'Sensitivity Updated',
      `New threshold: ${newThreshold}\n${increase ? 'Less' : 'More'} sensitive to blur`,
    );
  };

  if (!device) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No camera device available</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.cameraContainer}>
        <Camera
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={true}
          frameProcessor={frameProcessor}
        />

        {/* Blur Status Overlay */}
        <View style={styles.statusOverlay}>
          <View
            style={[
              styles.statusIndicator,
              isBlurry ? styles.blurryStatus : styles.sharpStatus,
            ]}>
            <Text style={styles.statusText}>
              {isBlurry ? 'BLURRY' : 'SHARP'}
            </Text>
            {__DEV__ && (
              <Text style={styles.scoreText}>
                Score: {blurScore.toFixed(1)}
              </Text>
            )}
          </View>
        </View>

        {/* Blur History */}
        <View style={styles.historyContainer}>
          <Text style={styles.historyTitle}>Recent History:</Text>
          <View style={styles.historyDots}>
            {blurHistory.map((wasBlurry, index) => (
              <View
                key={index}
                style={[
                  styles.historyDot,
                  wasBlurry ? styles.blurryDot : styles.sharpDot,
                ]}
              />
            ))}
          </View>
        </View>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <Text style={styles.controlsTitle}>Blur Detection Controls</Text>

        <View style={styles.buttonRow}>
          <Text
            style={styles.button}
            onPress={() => handleSensitivityChange(false)}>
            More Sensitive
          </Text>
          <Text
            style={styles.button}
            onPress={() => handleSensitivityChange(true)}>
            Less Sensitive
          </Text>
        </View>

        <Text style={styles.infoText}>
          Current Status: {isBlurry ? 'Image is blurry' : 'Image is sharp'}
        </Text>
        <Text style={styles.infoText}>
          Blur Score: {blurScore.toFixed(2)} (lower = more blurry)
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  statusOverlay: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 1000,
  },
  statusIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    minWidth: 80,
  },
  sharpStatus: {
    backgroundColor: colors.greenDark,
  },
  blurryStatus: {
    backgroundColor: colors.red400,
  },
  statusText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  scoreText: {
    color: colors.white,
    fontSize: 10,
    marginTop: 2,
  },
  historyContainer: {
    position: 'absolute',
    top: 20,
    left: 20,
    backgroundColor: colors.transBlack70,
    padding: 10,
    borderRadius: 10,
  },
  historyTitle: {
    color: colors.white,
    fontSize: 12,
    marginBottom: 5,
  },
  historyDots: {
    flexDirection: 'row',
    gap: 4,
  },
  historyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  sharpDot: {
    backgroundColor: colors.greenDark,
  },
  blurryDot: {
    backgroundColor: colors.red400,
  },
  controls: {
    backgroundColor: colors.white,
    padding: 20,
  },
  controlsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  button: {
    backgroundColor: colors.darkBlue500,
    color: colors.white,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    textAlign: 'center',
    fontSize: 14,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    textAlign: 'center',
  },
  errorText: {
    color: colors.red400,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 50,
  },
});

export default BlurDetectionExample;
