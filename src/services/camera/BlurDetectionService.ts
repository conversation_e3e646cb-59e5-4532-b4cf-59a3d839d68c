/**
 * BlurDetectionService provides real-time blur detection for camera frames
 * using Laplacian variation algorithm with react-native-worklets-core
 */

import { Worklets } from 'react-native-worklets-core';

export interface BlurDetectionResult {
  isBlurry: boolean;
  blurScore: number;
  threshold: number;
}

export interface BlurDetectionConfig {
  threshold?: number;
  sampleSize?: number;
  enableLogging?: boolean;
}

/**
 * Default configuration for blur detection
 */
const DEFAULT_CONFIG: Required<BlurDetectionConfig> = {
  threshold: 100, // Lower values = more sensitive to blur
  sampleSize: 100, // Size to resize frame for processing (100x100)
  enableLogging: false,
};

/**
 * Worklet function to calculate Laplacian variance for blur detection
 * This runs on the camera thread for optimal performance
 */
const calculateLaplacianVariance = Worklets.createRunOnJS((
  imageData: Uint8Array,
  width: number,
  height: number,
  threshold: number,
  enableLogging: boolean
): BlurDetectionResult => {
  'worklet';
  
  try {
    // Convert to grayscale if needed (assuming RGBA format)
    const grayscale = new Array(width * height);
    for (let i = 0; i < width * height; i++) {
      const pixelIndex = i * 4; // RGBA
      // Grayscale conversion using luminance formula
      grayscale[i] = Math.round(
        0.299 * imageData[pixelIndex] +     // R
        0.587 * imageData[pixelIndex + 1] + // G
        0.114 * imageData[pixelIndex + 2]   // B
      );
    }

    // Apply Laplacian kernel
    const laplacian = new Array(width * height).fill(0);
    
    // Laplacian kernel: [0, -1, 0; -1, 4, -1; 0, -1, 0]
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;
        laplacian[idx] = 
          -1 * grayscale[(y - 1) * width + x] +     // top
          -1 * grayscale[y * width + (x - 1)] +     // left
          4 * grayscale[y * width + x] +            // center
          -1 * grayscale[y * width + (x + 1)] +     // right
          -1 * grayscale[(y + 1) * width + x];      // bottom
      }
    }

    // Calculate variance of Laplacian
    const mean = laplacian.reduce((sum, val) => sum + val, 0) / laplacian.length;
    const variance = laplacian.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / laplacian.length;

    const isBlurry = variance < threshold;

    if (enableLogging) {
      console.log(`[BlurDetection] Variance: ${variance.toFixed(2)}, Threshold: ${threshold}, Blurry: ${isBlurry}`);
    }

    return {
      isBlurry,
      blurScore: variance,
      threshold,
    };
  } catch (error) {
    if (enableLogging) {
      console.error('[BlurDetection] Error in calculateLaplacianVariance:', error);
    }
    return {
      isBlurry: false,
      blurScore: 0,
      threshold,
    };
  }
});

/**
 * BlurDetectionService class for managing blur detection functionality
 */
export class BlurDetectionService {
  private config: Required<BlurDetectionConfig>;
  private isProcessing: boolean = false;

  constructor(config: BlurDetectionConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Update the blur detection configuration
   */
  updateConfig(newConfig: Partial<BlurDetectionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): Required<BlurDetectionConfig> {
    return { ...this.config };
  }

  /**
   * Process a camera frame for blur detection
   * This is the main method to be called from frame processors
   */
  async processFrame(
    imageData: Uint8Array,
    width: number,
    height: number
  ): Promise<BlurDetectionResult | null> {
    if (this.isProcessing) {
      // Skip frame if still processing previous one
      return null;
    }

    this.isProcessing = true;

    try {
      // Resize frame for faster processing if needed
      const { resizedData, resizedWidth, resizedHeight } = this.resizeFrame(
        imageData,
        width,
        height,
        this.config.sampleSize
      );

      const result = await calculateLaplacianVariance(
        resizedData,
        resizedWidth,
        resizedHeight,
        this.config.threshold,
        this.config.enableLogging
      );

      return result;
    } catch (error) {
      if (this.config.enableLogging) {
        console.error('[BlurDetectionService] Error processing frame:', error);
      }
      return null;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Resize frame data for faster processing
   * Simple nearest-neighbor downsampling
   */
  private resizeFrame(
    imageData: Uint8Array,
    originalWidth: number,
    originalHeight: number,
    targetSize: number
  ): { resizedData: Uint8Array; resizedWidth: number; resizedHeight: number } {
    // Calculate new dimensions maintaining aspect ratio
    const aspectRatio = originalWidth / originalHeight;
    let newWidth: number;
    let newHeight: number;

    if (aspectRatio > 1) {
      newWidth = targetSize;
      newHeight = Math.round(targetSize / aspectRatio);
    } else {
      newHeight = targetSize;
      newWidth = Math.round(targetSize * aspectRatio);
    }

    const resizedData = new Uint8Array(newWidth * newHeight * 4); // RGBA
    const scaleX = originalWidth / newWidth;
    const scaleY = originalHeight / newHeight;

    for (let y = 0; y < newHeight; y++) {
      for (let x = 0; x < newWidth; x++) {
        const srcX = Math.floor(x * scaleX);
        const srcY = Math.floor(y * scaleY);
        const srcIndex = (srcY * originalWidth + srcX) * 4;
        const destIndex = (y * newWidth + x) * 4;

        // Copy RGBA values
        resizedData[destIndex] = imageData[srcIndex];         // R
        resizedData[destIndex + 1] = imageData[srcIndex + 1]; // G
        resizedData[destIndex + 2] = imageData[srcIndex + 2]; // B
        resizedData[destIndex + 3] = imageData[srcIndex + 3]; // A
      }
    }

    return {
      resizedData,
      resizedWidth: newWidth,
      resizedHeight: newHeight,
    };
  }

  /**
   * Check if the service is currently processing a frame
   */
  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }

  /**
   * Create a frame processor function for use with react-native-vision-camera
   */
  createFrameProcessor(
    onBlurDetected: (result: BlurDetectionResult) => void
  ): (frame: any) => void {
    return Worklets.createRunOnJS((frame: any) => {
      'worklet';
      
      try {
        // Extract frame data (this will depend on the frame format from vision-camera)
        // For now, we'll assume the frame provides the necessary data
        if (frame && frame.pixelFormat && frame.width && frame.height) {
          // Convert frame to Uint8Array (implementation depends on frame format)
          // This is a placeholder - actual implementation will depend on vision-camera frame structure
          const imageData = this.extractImageDataFromFrame(frame);
          
          if (imageData) {
            this.processFrame(imageData, frame.width, frame.height)
              .then(result => {
                if (result) {
                  onBlurDetected(result);
                }
              })
              .catch(error => {
                if (this.config.enableLogging) {
                  console.error('[BlurDetectionService] Frame processor error:', error);
                }
              });
          }
        }
      } catch (error) {
        if (this.config.enableLogging) {
          console.error('[BlurDetectionService] Frame processor error:', error);
        }
      }
    });
  }

  /**
   * Extract image data from vision-camera frame
   * This method needs to be implemented based on the actual frame format
   */
  private extractImageDataFromFrame(frame: any): Uint8Array | null {
    // TODO: Implement based on react-native-vision-camera frame format
    // This will depend on the specific frame format (YUV, RGB, etc.)
    // For now, return null as placeholder
    return null;
  }
}

/**
 * Default instance for easy usage
 */
export const blurDetectionService = new BlurDetectionService();
