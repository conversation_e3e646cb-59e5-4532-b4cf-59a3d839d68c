/**
 * Tests for BlurDetectionService
 */

// Mock react-native-worklets-core
jest.mock('react-native-worklets-core', () => ({
  Worklets: {
    createRunOnJS: jest.fn(fn => fn),
  },
}));

// Mock react-native-vision-camera
jest.mock('react-native-vision-camera', () => ({
  Frame: {},
}));

import { BlurDetectionService } from '~/services/camera/BlurDetectionService';

describe('BlurDetectionService', () => {
  let service: BlurDetectionService;

  beforeEach(() => {
    service = new BlurDetectionService({
      threshold: 100,
      sampleSize: 50,
      enableLogging: false,
      skipFrames: 0, // Process every frame for testing
    });
  });

  describe('Configuration', () => {
    it('should initialize with default config', () => {
      const defaultService = new BlurDetectionService();
      const config = defaultService.getConfig();

      expect(config.threshold).toBe(100);
      expect(config.sampleSize).toBe(100);
      expect(config.enableLogging).toBe(false);
      expect(config.skipFrames).toBe(2);
    });

    it('should update configuration', () => {
      service.updateConfig({ threshold: 150, enableLogging: true });
      const config = service.getConfig();

      expect(config.threshold).toBe(150);
      expect(config.enableLogging).toBe(true);
      expect(config.sampleSize).toBe(50); // Should remain unchanged
    });
  });

  describe('Frame Processing', () => {
    it('should process a sharp image correctly', () => {
      // Create a mock sharp image (high contrast pattern)
      const width = 50;
      const height = 50;
      const imageData = new Uint8Array(width * height * 4); // RGBA

      // Create a checkerboard pattern (high contrast = sharp)
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const index = (y * width + x) * 4;
          const isWhite = (Math.floor(x / 5) + Math.floor(y / 5)) % 2 === 0;
          const value = isWhite ? 255 : 0;

          imageData[index] = value; // R
          imageData[index + 1] = value; // G
          imageData[index + 2] = value; // B
          imageData[index + 3] = 255; // A
        }
      }

      const result = service.processFrame(imageData, width, height);

      expect(result).not.toBeNull();
      expect(result!.isBlurry).toBe(false);
      expect(result!.blurScore).toBeGreaterThan(100); // Should be above threshold
    });

    it('should process a blurry image correctly', () => {
      // Create a mock blurry image (low contrast)
      const width = 50;
      const height = 50;
      const imageData = new Uint8Array(width * height * 4); // RGBA

      // Create a uniform gray image (low contrast = blurry)
      for (let i = 0; i < imageData.length; i += 4) {
        imageData[i] = 128; // R
        imageData[i + 1] = 128; // G
        imageData[i + 2] = 128; // B
        imageData[i + 3] = 255; // A
      }

      const result = service.processFrame(imageData, width, height);

      expect(result).not.toBeNull();
      expect(result!.isBlurry).toBe(true);
      expect(result!.blurScore).toBeLessThan(100); // Should be below threshold
    });

    it('should skip frames based on skipFrames setting', () => {
      const serviceWithSkip = new BlurDetectionService({ skipFrames: 2 });
      const width = 10;
      const height = 10;
      const imageData = new Uint8Array(width * height * 4);

      // First call should return null (frame 1, skip)
      const result1 = serviceWithSkip.processFrame(imageData, width, height);
      expect(result1).toBeNull();

      // Second call should return null (frame 2, skip)
      const result2 = serviceWithSkip.processFrame(imageData, width, height);
      expect(result2).toBeNull();

      // Third call should process (frame 3, process)
      const result3 = serviceWithSkip.processFrame(imageData, width, height);
      expect(result3).not.toBeNull();
    });
  });

  describe('Frame Processor Creation', () => {
    it('should create a frame processor function', () => {
      const mockCallback = jest.fn();
      const frameProcessor = service.createFrameProcessor(mockCallback);

      expect(typeof frameProcessor).toBe('function');
    });
  });

  describe('Resize Frame', () => {
    it('should resize frame data correctly', () => {
      // Test the private resizeFrame method through processFrame
      const width = 100;
      const height = 100;
      const imageData = new Uint8Array(width * height * 4);

      // Fill with test pattern
      for (let i = 0; i < imageData.length; i += 4) {
        imageData[i] = 255; // R
        imageData[i + 1] = 0; // G
        imageData[i + 2] = 0; // B
        imageData[i + 3] = 255; // A
      }

      const result = service.processFrame(imageData, width, height);

      // Should successfully process the resized frame
      expect(result).not.toBeNull();
      expect(typeof result!.blurScore).toBe('number');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid image data gracefully', () => {
      const result = service.processFrame(new Uint8Array(0), 0, 0);

      expect(result).toBeNull();
    });

    it('should handle malformed image data', () => {
      // Too small buffer for the given dimensions
      const imageData = new Uint8Array(10); // Way too small
      const result = service.processFrame(imageData, 100, 100);

      expect(result).toBeNull();
    });
  });
});
